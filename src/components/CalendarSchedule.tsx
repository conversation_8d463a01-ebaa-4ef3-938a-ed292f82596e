'use client'

import { useEffect, useState, useRef } from "react";
import { cn } from "@/lib/utils";

export default function CalendarSchedule() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Check if we're on mobile
    const checkMobile = () => {
      const isMobileView = window.innerWidth < 768;
      console.log("Calendar: Screen width check - width:", window.innerWidth, "isMobile:", isMobileView);
      setIsMobile(isMobileView);
    };

    // Initial check
    checkMobile();

    // Add resize listener
    window.addEventListener('resize', checkMobile);

    // Set a timeout to ensure the loading state doesn't get stuck
    loadingTimeoutRef.current = setTimeout(() => {
      if (!isLoaded) {
        console.log("Calendar: Force setting loaded state after timeout");
        setIsLoaded(true);
      }
    }, 5000);

    return () => {
      window.removeEventListener('resize', checkMobile);
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, [isLoaded]);

  // Handle iframe load event
  const handleIframeLoad = () => {
    console.log("Calendar: Iframe loaded");
    setIsLoaded(true);
  };

  // Calculate appropriate height based on device
  const calendarHeight = isMobile ? "600px" : "700px";

  // Construct the Cal.com URL with appropriate parameters
  const calendarUrl = "https://cal.com/francesco-oddo/30min?embed=true&theme=dark&layout=month_view&hideEventTypeDetails=true&brandColor=%23FF6B6B";

  return (
    <div className="w-full">
      <div
        className={cn(
          "w-full rounded-xl transition-opacity duration-500 relative",
          isLoaded ? "opacity-100" : "opacity-0"
        )}
        style={{
          height: calendarHeight,
          overflow: "hidden" // Ensure the container doesn't overflow
        }}
      >
        <iframe
          ref={iframeRef}
          src={calendarUrl}
          onLoad={handleIframeLoad}
          style={{
            width: "100%",
            height: "100%",
            border: "none",
            borderRadius: "12px",
          }}
          allow="camera; microphone; fullscreen; display-capture; autoplay"
          loading="lazy"
        />
      </div>

      {!isLoaded && (
        <div className={`w-full absolute top-0 left-0 z-10 ${isMobile ? 'h-[600px]' : 'h-[700px]'} flex items-center justify-center bg-[#1A1F2A]/80 rounded-xl`}>
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 rounded-full border-4 border-[#FF6B6B] border-t-transparent animate-spin mb-4"></div>
            <p className="text-gray-400">Loading calendar...</p>
          </div>
        </div>
      )}
    </div>
  );
}
