'use client'

import { useState } from "react"
import { ArrowUpRight } from 'lucide-react'
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import Image from "next/image"

function SocialCard({ 
  platform, 
  username, 
  description, 
  profileImage, 
  buttonText, 
  link 
}: { 
  platform: 'twitter' | 'youtube' | 'telegram'
  username: string
  description: string
  profileImage: string
  buttonText: string
  link: string
}) {
  const [isHovered, setIsHovered] = useState(false)

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'twitter': return '#1DA1F2'
      case 'youtube': return '#FF0000'
      case 'telegram': return '#0088cc'
      default: return '#1DA1F2'
    }
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'twitter':
        return (
          <svg viewBox="0 0 24 24" className="w-4 h-4 sm:w-7 sm:h-7 text-white fill-current">
            <path d="M23.643 4.937c-.835.37-1.732.62-2.675.733.962-.576 1.7-1.49 2.048-2.578-.9.534-1.897.922-2.958 1.13-.85-.904-2.06-1.47-3.4-1.47-2.572 0-4.658 2.086-4.658 4.66 0 .364.042.718.12 1.06-3.873-.195-7.304-2.05-9.602-4.868-.4.69-.63 1.49-.63 2.342 0 1.616.823 3.043 2.072 3.878-.764-.025-1.482-.234-2.11-.583v.06c0 2.257 1.605 4.14 3.737 4.568-.392.106-.803.162-1.227.162-.3 0-.593-.028-.877-.082.593 1.85 2.313 3.198 4.352 3.234-1.595 1.25-3.604 1.995-5.786 1.995-.376 0-.747-.022-1.112-.065 2.062 1.323 4.51 2.093 7.14 2.093 8.57 0 13.255-7.098 13.255-13.254 0-.2-.005-.402-.014-.602.91-.658 1.7-1.477 2.323-2.41z" />
          </svg>
        )
      case 'youtube':
        return (
          <svg viewBox="0 0 24 24" className="w-4 h-4 sm:w-7 sm:h-7 text-white fill-current">
            <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
          </svg>
        )
      case 'telegram':
        return (
          <svg viewBox="0 0 24 24" className="w-4 h-4 sm:w-7 sm:h-7 text-white fill-current">
            <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
          </svg>
        )
      default:
        return null
    }
  }

  return (
    <Card 
      className="w-80 flex-shrink-0 overflow-hidden relative bg-[#1A1F2A] rounded-3xl shadow-lg"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Expanding background */}
      <div 
        className="absolute top-2 right-2 sm:top-3 sm:right-3 w-9 h-9 sm:w-[53px] sm:h-[53px] rounded-lg transition-all duration-500 ease-in-out"
        style={{
          backgroundColor: getPlatformColor(platform),
          transform: isHovered ? 'scale(25)' : 'scale(1)',
          transformOrigin: 'center',
        }}
      />
      
      {/* Platform Logo */}
      <div 
        className="absolute top-2 right-2 sm:top-3 sm:right-3 z-20 rounded-lg w-9 h-9 sm:w-[53px] sm:h-[53px] flex items-center justify-center"
        style={{ backgroundColor: getPlatformColor(platform) }}
      >
        {getPlatformIcon(platform)}
      </div>

      {/* Content */}
      <div className="p-3 sm:p-4 relative z-10">
        <div className="flex items-center gap-2 sm:gap-3 pr-12 sm:pr-16">
          <div className="h-8 w-8 sm:h-10 sm:w-10 rounded-full overflow-hidden relative">
            <Image
              src={profileImage}
              alt={`${username} profile`}
              fill
              className="object-cover"
            />
          </div>
          <div className="flex flex-col">
            <span className={`font-medium text-sm sm:text-base transition-colors duration-300 ${
              isHovered ? 'text-white' : 'text-white'
            }`}>
              {username}
            </span>
            <span className={`text-xs sm:text-sm transition-colors duration-300 ${
              isHovered ? 'text-white/70' : 'text-white/70'
            }`}>
              @{platform === 'telegram' ? username : `${username}_`}
            </span>
          </div>
        </div>
        <p className={`mt-4 sm:mt-6 pt-[10px] text-sm sm:text-base transition-colors duration-300 ${
          isHovered ? 'text-white' : 'text-white'
        }`}>
          {description}
        </p>
        <Button
          variant="outline"
          className={`mt-3 sm:mt-4 w-full transition-all duration-300 rounded-full text-sm sm:text-base ${
            isHovered 
              ? 'bg-white/10 text-white border-white/20 hover:bg-white/20' 
              : 'bg-background text-foreground hover:bg-accent border-[#E5E5E5]'
          }`}
          asChild
        >
          <a href={link} target="_blank" rel="noopener noreferrer" className="flex items-center justify-center py-1 sm:py-2">
            {buttonText}
            <ArrowUpRight className="ml-1 sm:ml-2 h-3 w-3 sm:h-4 sm:w-4" />
          </a>
        </Button>
      </div>
    </Card>
  )
}

export default function SocialCards() {
  return (
    <div className="w-full overflow-x-auto p-4">
      <div className="flex gap-4 pb-4">
        <SocialCard
          platform="twitter"
          username="Francesco Oddo"
          description="building in web3 : always exploring"
          profileImage="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/9d2bb4a0723ca31e1f9dc4f6f04e03c9-XfJdlIKEwkJbNaa6bjjmpFAw0nvFQS.png"
          buttonText="Read mid tweets"
          link="https://x.com/OxFrancesco_"
        />
        <SocialCard
          platform="youtube"
          username="Francesco Oddo"
          description="watch mid web3 videos"
          profileImage="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/9d2bb4a0723ca31e1f9dc4f6f04e03c9-XfJdlIKEwkJbNaa6bjjmpFAw0nvFQS.png"
          buttonText="Watch videos"
          link="https://www.youtube.com/@OxFrancesco"
        />
        <SocialCard
          platform="telegram"
          username="Francesco_Oddo"
          description="get in contact with me!"
          profileImage="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/9d2bb4a0723ca31e1f9dc4f6f04e03c9-XfJdlIKEwkJbNaa6bjjmpFAw0nvFQS.png"
          buttonText="Keep in touch!"
          link="https://t.me/Francesco_Oddo"
        />
      </div>
    </div>
  )
}
