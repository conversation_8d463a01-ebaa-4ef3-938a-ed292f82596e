"use client"

import React, { useEffect, useState, useRef, useCallback } from "react"
import { motion, AnimatePresence } from "framer-motion"
import Link from "next/link"
import { LucideIcon } from "lucide-react"
import { cn } from "@/lib/utils"

interface NavItem {
  name: string
  url: string
  icon: LucideIcon
}

interface NavBarProps {
  items: NavItem[]
  className?: string
  defaultActive?: string
}

export function AnimeNavBar({ items, defaultActive = "Home" }: NavBarProps) {
  const [mounted, setMounted] = useState(false)
  const [hoveredTab, setHoveredTab] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<string>(defaultActive)
  const observerRef = useRef<IntersectionObserver | null>(null);
  const initialCheckTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Memoize the callback to prevent it from changing on every render
  const observerCallback = useCallback((entries: IntersectionObserverEntry[]) => {
    console.log(`NavBar: Observer callback triggered with ${entries.length} entries`);

    interface BestMatchType {
      entry: IntersectionObserverEntry;
      item: NavItem;
    }

    let bestMatch: BestMatchType | null = null;

    entries.forEach(entry => {
      if (!entry.target.id) {
        console.log("NavBar: Entry has no ID, skipping", entry.target);
        return;
      }

      console.log(`NavBar: Processing entry for #${entry.target.id}, isIntersecting: ${entry.isIntersecting}`);

      if (entry.isIntersecting) {
        const sectionId = `#${entry.target.id}`;
        // Find the matching item based on the section ID
        const matchingItem = items.find(item => item.url === sectionId);

        if (matchingItem) {
          console.log(`NavBar: Found matching item for ${sectionId}:`, matchingItem.name);
          // Prioritize the highest element on the page that is intersecting
          if (!bestMatch || entry.boundingClientRect.top < bestMatch.entry.boundingClientRect.top) {
            bestMatch = { entry: entry, item: matchingItem };
          }
        } else {
          console.log(`NavBar: No matching item found for ${sectionId}`);
        }
      }
    });

    if (bestMatch) {
      // Using type assertion to help TypeScript understand the structure
      const match = bestMatch as { item: NavItem };
      const itemName = match.item.name;
      console.log(`NavBar: Best match found:`, itemName);
      // Update the active tab state if the best match is different from the current active tab
      setActiveTab(prevActiveTab => {
        if (prevActiveTab !== itemName) {
          console.log("NavBar: Setting active tab via observer:", itemName);
          return itemName;
        }
        console.log("NavBar: No change needed, keeping active tab:", prevActiveTab);
        return prevActiveTab; // No change needed
      });
    } else {
      console.log("NavBar: No best match found, active tab remains unchanged");
    }
    // If no relevant section is intersecting, the active tab remains unchanged.
  }, [items]); // Dependency: items array reference

  useEffect(() => {
    setMounted(true)
    console.log("NavBar: Component mounted");

    const observerOptions = {
      root: null,
      rootMargin: "-120px 0px -70% 0px", // Adjust margins as needed
      threshold: 0, // Trigger as soon as visibility changes
    };

    console.log("NavBar: Observer options set", observerOptions);

    // Disconnect previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Create and store the new observer instance
    observerRef.current = new IntersectionObserver(observerCallback, observerOptions);
    const currentObserver = observerRef.current; // Capture current observer for cleanup

    // Map items to section elements
    const sections = items.map(item => {
      try {
        if (item.url && item.url.startsWith('#') && item.url.length > 1) {
          const section = document.querySelector(item.url);
          console.log(`NavBar: Looking for section ${item.url}`, section ? "found" : "not found");
          return section;
        }
      } catch (e) {
        console.error(`NavBar: Invalid selector: ${item.url}`, e);
      }
      return null;
    }).filter(el => el !== null) as Element[];

    console.log(`NavBar: Found ${sections.length} sections to observe`);

    if (sections.length === 0) {
      console.warn("NavBar: No sections found to observe.");
      return; // Exit if no sections
    }

    // Observe each section
    sections.forEach(section => {
      currentObserver.observe(section);
    });

    // Clear previous timeout if effect re-runs before timeout completes
    if (initialCheckTimeoutRef.current) {
      clearTimeout(initialCheckTimeoutRef.current);
    }

    // Perform initial check after a short delay to allow layout stabilization
    initialCheckTimeoutRef.current = setTimeout(() => {
      const initialEntries = sections.map(section => {
        const rect = section.getBoundingClientRect();
        const rootBounds = document.documentElement.getBoundingClientRect();
        const topMargin = parseInt(observerOptions.rootMargin.split(' ')[0], 10);
        const bottomMarginPercent = parseInt(observerOptions.rootMargin.split(' ')[2].replace('%', ''), 10);
        const bottomMarginPx = window.innerHeight * (bottomMarginPercent / 100);
        const isIntersecting = rect.top < (window.innerHeight + bottomMarginPx) && rect.bottom > topMargin;

        // Construct a mock IntersectionObserverEntry
        return {
          target: section,
          isIntersecting: isIntersecting,
          boundingClientRect: rect,
          intersectionRatio: isIntersecting ? 1 : 0,
          intersectionRect: isIntersecting ? rect : null,
          rootBounds: rootBounds,
          time: performance.now()
        } as IntersectionObserverEntry;
      });
      const validEntries = initialEntries.filter(entry => entry.target);
      if (validEntries.length > 0) {
         // Manually trigger the callback with initial state
         observerCallback(validEntries);
      }
    }, 150); // Delay for initial check

    // Cleanup function: disconnect observer and clear timeout
    return () => {
      if (initialCheckTimeoutRef.current) {
        clearTimeout(initialCheckTimeoutRef.current);
      }
      // Use the captured observer instance for cleanup
      if (currentObserver) {
         currentObserver.disconnect();
      }
      observerRef.current = null; // Clear the ref
    };

  // Dependencies: Only run when items or the memoized callback changes.
  // defaultActive is only for initial state, not needed as a dependency here.
  }, [items, observerCallback]);


  if (!mounted) return null

  return (
    <div className="fixed top-0 left-0 right-0 z-[9999]">
      <div className="flex justify-center mt-4 md:mt-8 px-2">
        <motion.div
          className="flex items-center gap-1 md:gap-2 bg-[#1A1F2A]/80 border border-[#FF6B6B]/10 backdrop-blur-md py-2 px-1 md:px-2 rounded-xl shadow-lg relative"
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{
            type: "spring",
            stiffness: 260,
            damping: 20,
          }}
        >
          {items.map((item) => {
            const Icon = item.icon
            const isActive = activeTab === item.name
            const isHovered = hoveredTab === item.name

            return (
              <Link
                key={item.name}
                href={item.url}
                onClick={() => {
                  setActiveTab(item.name)
                  const element = document.querySelector(item.url)
                  element?.scrollIntoView({ behavior: 'smooth' })
                }}
                onMouseEnter={() => setHoveredTab(item.name)}
                onMouseLeave={() => setHoveredTab(null)}
                className={cn(
                  "relative cursor-pointer text-sm font-medium px-2 md:px-4 py-2 rounded-lg transition-all duration-300",
                  "text-gray-400 hover:text-white",
                  isActive && "text-white"
                )}
              >
                {isActive && (
                  <motion.div
                    className="absolute inset-0 rounded-lg -z-10 overflow-hidden"
                    layoutId="active-tab-indicator" // Added layoutId for smooth transition
                    initial={false} // No initial animation needed here
                    animate={{ opacity: 1 }} // Ensure it's visible
                    transition={{ // Smooth transition for background
                      type: "spring",
                      stiffness: 300,
                      damping: 30
                    }}
                  >
                    {/* Background elements */}
                    <div className="absolute inset-0 bg-[#FF6B6B]/25 rounded-lg blur-md" />
                    <div className="absolute inset-[-4px] bg-[#FF6B6B]/20 rounded-lg blur-xl" />
                    <div className="absolute inset-[-8px] bg-[#FF6B6B]/15 rounded-lg blur-2xl" />
                    <div className="absolute inset-[-12px] bg-[#FF6B6B]/5 rounded-lg blur-3xl" />

                    {/* Shine animation */}
                    <div
                      className="absolute inset-0 bg-gradient-to-r from-[#FF6B6B]/0 via-[#FF6B6B]/20 to-[#FF6B6B]/0"
                      style={{
                        animation: "shine 3s ease-in-out infinite"
                      }}
                    />
                  </motion.div>
                )}

                <motion.span
                  className="hidden md:inline relative z-10"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  {item.name}
                </motion.span>
                <motion.span
                  className="md:hidden relative z-10"
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Icon size={16} className="text-[#FF6B6B]" />
                </motion.span>

                <AnimatePresence>
                  {isHovered && !isActive && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      className="absolute inset-0 bg-white/10 rounded-full -z-10"
                    />
                  )}
                </AnimatePresence>

                {isActive && (
                  <motion.div
                    layoutId="anime-mascot"
                    className="absolute -top-10 md:-top-12 left-1/2 -translate-x-1/2 pointer-events-none"
                    initial={false}
                    transition={{
                      type: "spring",
                      stiffness: 300,
                      damping: 30,
                    }}
                  >
                    <div className="relative w-10 h-10 md:w-12 md:h-12">
                      <motion.div
                        className="absolute w-8 h-8 md:w-10 md:h-10 bg-white rounded-full left-1/2 -translate-x-1/2"
                        animate={
                          hoveredTab ? {
                            scale: [1, 1.1, 1],
                            rotate: [0, -5, 5, 0],
                            transition: {
                              duration: 0.5,
                              ease: "easeInOut"
                            }
                          } : {
                            y: [0, -3, 0],
                            transition: {
                              duration: 2,
                              repeat: Infinity,
                              ease: "easeInOut"
                            }
                          }
                        }
                      >
                        <motion.div
                          className="absolute w-2 h-2 bg-black rounded-full"
                          animate={
                            hoveredTab ? {
                              scaleY: [1, 0.2, 1],
                              transition: {
                                duration: 0.2,
                                times: [0, 0.5, 1]
                              }
                            } : {}
                          }
                          style={{ left: '25%', top: '40%' }}
                        />
                        <motion.div
                          className="absolute w-2 h-2 bg-black rounded-full"
                          animate={
                            hoveredTab ? {
                              scaleY: [1, 0.2, 1],
                              transition: {
                                duration: 0.2,
                                times: [0, 0.5, 1]
                              }
                            } : {}
                          }
                          style={{ right: '25%', top: '40%' }}
                        />
                        <motion.div
                          className="absolute w-2 h-1.5 bg-pink-300 rounded-full"
                          animate={{
                            opacity: hoveredTab ? 0.8 : 0.6
                          }}
                          style={{ left: '15%', top: '55%' }}
                        />
                        <motion.div
                          className="absolute w-2 h-1.5 bg-pink-300 rounded-full"
                          animate={{
                            opacity: hoveredTab ? 0.8 : 0.6
                          }}
                          style={{ right: '15%', top: '55%' }}
                        />

                        <motion.div
                          className="absolute w-4 h-2 border-b-2 border-black rounded-full"
                          animate={
                            hoveredTab ? {
                              scaleY: 1.5,
                              y: -1
                            } : {
                              scaleY: 1,
                              y: 0
                            }
                          }
                          style={{ left: '30%', top: '60%' }}
                        />
                        <AnimatePresence>
                          {hoveredTab && (
                            <>
                              <motion.div
                                initial={{ opacity: 0, scale: 0 }}
                                animate={{ opacity: 1, scale: 1 }}
                                exit={{ opacity: 0, scale: 0 }}
                                className="absolute -top-1 -right-1 w-2 h-2 text-yellow-300"
                              >
                                ✨
                              </motion.div>
                              <motion.div
                                initial={{ opacity: 0, scale: 0 }}
                                animate={{ opacity: 1, scale: 1 }}
                                exit={{ opacity: 0, scale: 0 }}
                                transition={{ delay: 0.1 }}
                                className="absolute -top-2 left-0 w-2 h-2 text-yellow-300"
                              >
                                ✨
                              </motion.div>
                            </>
                          )}
                        </AnimatePresence>
                      </motion.div>
                      <motion.div
                        className="absolute -bottom-1 left-1/2 w-4 h-4 -translate-x-1/2"
                        animate={
                          hoveredTab ? {
                            y: [0, -4, 0],
                            transition: {
                              duration: 0.3,
                              repeat: Infinity,
                              repeatType: "reverse"
                            }
                          } : {
                            y: [0, 2, 0],
                            transition: {
                              duration: 1,
                              repeat: Infinity,
                              ease: "easeInOut",
                              delay: 0.5
                            }
                          }
                        }
                      >
                        <div className="w-full h-full bg-white rotate-45 transform origin-center" />
                      </motion.div>
                    </div>
                  </motion.div>
                )}
              </Link>
            )
          })}
        </motion.div>
      </div>
    </div>
  )
}