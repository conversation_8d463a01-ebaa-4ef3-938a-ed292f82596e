"use client";
import React, {
  createContext,
  useState,
  useContext,
  useRef,
  useEffect,
} from "react";
import {
  motion,
  useMotionValue,
  useSpring,
} from "framer-motion";

const MouseEnterContext = createContext<
  [boolean, React.Dispatch<React.SetStateAction<boolean>>] | undefined
>(undefined);

export const CardContainer = ({
  children,
  className,
  containerClassName,
  href,
  target = "_blank",
}: {
  children: React.ReactNode;
  className?: string;
  containerClassName?: string;
  href?: string;
  target?: string;
}) => {
  const [isMouseEntered, setIsMouseEntered] = useState(false);
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const { clientX, clientY } = e;
    const { left, top } = e.currentTarget.getBoundingClientRect();
    mouseX.set(clientX - left);
    mouseY.set(clientY - top);
  };

  const handleClick = () => {
    if (href) {
      window.open(href, target, 'noopener,noreferrer');
    }
  };

  return (
    <MouseEnterContext.Provider value={[isMouseEntered, setIsMouseEntered]}>
      <motion.div
        className={`${containerClassName} ${href ? 'cursor-pointer' : ''}`}
        onMouseMove={handleMouseMove}
        onMouseEnter={() => setIsMouseEntered(true)}
        onMouseLeave={() => setIsMouseEntered(false)}
        onClick={handleClick}
      >
        <div className={className}>{children}</div>
      </motion.div>
    </MouseEnterContext.Provider>
  );
};

export const CardBody = ({
  children,
  className,
  href,
  target = "_blank",
}: {
  children: React.ReactNode;
  className?: string;
  href?: string;
  target?: string;
}) => {
  const [isMouseEntered] = useMouseEnter();
  const ref = useRef<HTMLDivElement>(null);
  const rotateX = useSpring(0, { stiffness: 200, damping: 20 });
  const rotateY = useSpring(0, { stiffness: 200, damping: 20 });

  useEffect(() => {
    if (!ref.current) return;
    const { top, left, width, height } = ref.current.getBoundingClientRect();

    const handleMouseMove = (e: MouseEvent) => {
      const mouseX = e.clientX - left;
      const mouseY = e.clientY - top;
      const rotateXValue = isMouseEntered ? (mouseY - height / 2) / 20 : 0;
      const rotateYValue = isMouseEntered ? -(mouseX - width / 2) / 20 : 0;
      rotateX.set(rotateXValue);
      rotateY.set(rotateYValue);
    };

    window.addEventListener("mousemove", handleMouseMove);
    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
    };
  }, [isMouseEntered, rotateX, rotateY]);

  const handleClick = (e: React.MouseEvent) => {
    if (href) {
      e.preventDefault();
      window.open(href, target, 'noopener,noreferrer');
    }
  };

  return (
    <motion.div
      ref={ref}
      className={`${className} md:w-1/2 ${href ? 'cursor-pointer' : ''}`}
      style={{
        transformStyle: "preserve-3d",
        rotateX: rotateX,
        rotateY: rotateY,
      }}
      onClick={handleClick}
    >
      {children}
    </motion.div>
  );
};

export const CardItem = ({
  as: Component = "div",
  children,
  className,
  translateX = 0,
  translateY = 0,
  translateZ = 0,
  rotateX = 0,
  rotateY = 0,
  rotateZ = 0,
  href,
  target = "_blank",
  ...rest
}: {
  as?: React.ElementType;
  children: React.ReactNode;
  className?: string;
  translateX?: number | string;
  translateY?: number | string;
  translateZ?: number | string;
  rotateX?: number | string;
  rotateY?: number | string;
  rotateZ?: number | string;
  href?: string;
  target?: string;
}) => {
  const [isMouseEntered] = useMouseEnter();

  const style = {
    transform: isMouseEntered
      ? `translate3d(${translateX}px, ${translateY}px, ${translateZ}px)
         rotateX(${rotateX}deg) rotateY(${rotateY}deg) rotateZ(${rotateZ}deg)`
      : "translate3d(0, 0, 0) rotateX(0deg) rotateY(0deg) rotateZ(0deg)",
    transition: "transform 0.3s ease-out",
  };

  const handleClick = (e: React.MouseEvent) => {
    if (href) {
      e.preventDefault();
      window.open(href, target, 'noopener,noreferrer');
    }
  };

  return (
    <Component 
      className={`${className} ${href ? 'cursor-pointer' : ''}`} 
      style={style} 
      onClick={handleClick}
      {...rest}
    >
      {children}
    </Component>
  );
};

const useMouseEnter = () => {
  const context = useContext(MouseEnterContext);
  if (context === undefined) {
    throw new Error("useMouseEnter must be used within a MouseEnterProvider");
  }
  return context;
};

export default CardContainer;