'use client';

import {
  motion,
  MotionValue,
  useMotionValue,
  useSpring,
  useTransform,
  type SpringOptions,
} from 'framer-motion';
import {
  Children,
  cloneElement,
  createContext,
  useContext,
  useMemo,
  useRef,
} from 'react';
import { cn } from '@/lib/utils';

const DOCK_WIDTH = 64;
const DEFAULT_MAGNIFICATION = 80;
const DEFAULT_DISTANCE = 150;
const DEFAULT_PANEL_WIDTH = 48;

type DockContextType = {
  mouseY: MotionValue;
  spring: SpringOptions;
  magnification: number;
  distance: number;
};

const DockContext = createContext<DockContextType | undefined>(undefined);

export function VerticalDock({
  children,
  className,
  spring = { mass: 0.1, stiffness: 150, damping: 12 },
  magnification = DEFAULT_MAGNIFICATION,
  distance = DEFAULT_DISTANCE,
  panelWidth = DEFAULT_PANEL_WIDTH,
}: {
  children: React.ReactNode;
  className?: string;
  spring?: SpringOptions;
  magnification?: number;
  distance?: number;
  panelWidth?: number;
}) {
  const mouseY = useMotionValue(Infinity);
  const isHovered = useMotionValue(0);

  const maxWidth = useMemo(() => {
    return Math.max(DOCK_WIDTH, magnification + magnification / 2 + 4);
  }, [magnification]);

  const widthRow = useTransform(isHovered, [0, 1], [panelWidth, maxWidth]);
  const width = useSpring(widthRow, spring);

  return (
    <motion.div
      style={{
        width,
        scrollbarWidth: 'none',
      }}
      className='my-2 flex max-h-full flex-col items-center overflow-y-auto'
    >
      <motion.div
        onMouseMove={({ pageY }) => {
          isHovered.set(1);
          mouseY.set(pageY);
        }}
        onMouseLeave={() => {
          isHovered.set(0);
          mouseY.set(Infinity);
        }}
        className={cn(
          'my-auto flex h-fit flex-col gap-4 rounded-2xl py-4',
          className
        )}
        style={{ width: panelWidth }}
        role='toolbar'
        aria-label='Social links dock'
      >
        <DockContext.Provider value={{ mouseY, spring, distance, magnification }}>
          {children}
        </DockContext.Provider>
      </motion.div>
    </motion.div>
  );
}

export function DockItem({ children, className }: { children: React.ReactNode; className?: string }) {
  const ref = useRef<HTMLDivElement>(null);
  const context = useContext(DockContext);
  if (!context) throw new Error('DockItem must be used within VerticalDock');
  
  const { distance, magnification, mouseY, spring } = context;
  const isHovered = useMotionValue(0);

  const mouseDistance = useTransform(mouseY, (val) => {
    const domRect = ref.current?.getBoundingClientRect() ?? { y: 0, height: 0 };
    return val - domRect.y - domRect.height / 2;
  });

  const heightTransform = useTransform(
    mouseDistance,
    [-distance, 0, distance],
    [40, magnification, 40]
  );

  const height = useSpring(heightTransform, spring);

  return (
    <motion.div
      ref={ref}
      style={{ height }}
      onHoverStart={() => isHovered.set(1)}
      onHoverEnd={() => isHovered.set(0)}
      onFocus={() => isHovered.set(1)}
      onBlur={() => isHovered.set(0)}
      className={cn('relative flex items-center justify-center', className)}
      tabIndex={0}
      role='button'
    >
      {Children.map(children, (child) =>
        cloneElement(child as React.ReactElement, { height, isHovered })
      )}
    </motion.div>
  );
}

export function DockIcon({ children, className, ...rest }: {
  children: React.ReactNode;
  className?: string;
  height?: MotionValue<number>;
  isHovered?: MotionValue<number>;
}) {
  const height = rest['height'] as MotionValue<number>;
  const heightTransform = useTransform(height, (val) => val / 2);

  return (
    <motion.div
      style={{ height: heightTransform }}
      className={cn('flex items-center justify-center', className)}
    >
      {children}
    </motion.div>
  );
} 