'use client'

import React, { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
// Removed Tooltip imports
import { SiNextdotjs, SiNotion, SiVercel, SiGithub, SiSupabase } from "react-icons/si"
import Image from "next/image"
import { cn } from "@/lib/utils"; // Import cn

const icons = [
  { icon: () => <div className="w-12 h-12 flex items-center justify-center"><SiNextdotjs className="w-8 h-8" /></div>, name: "Next.js" },
  { icon: () => <div className="w-12 h-12 flex items-center justify-center"><SiNotion className="w-8 h-8" /></div>, name: "Notion" },
  { icon: () => <div className="w-12 h-12 flex items-center justify-center"><SiVercel className="w-8 h-8" /></div>, name: "Vercel" },
  { icon: () => <div className="w-12 h-12 flex items-center justify-center"><SiGithub className="w-8 h-8" /></div>, name: "GitHub" },
  { icon: () => <div className="w-12 h-12 flex items-center justify-center"><SiSupabase className="w-8 h-8" /></div>, name: "Supabase" },
  { icon: () => (
    <div className="w-12 h-12 flex items-center justify-center">
      <Image
        src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-2YTL6JCu6RPdR8EdrD6VIRlLKj9u5m.png"
        alt="n8n"
        width={40}
        height={40}
        className="rounded-lg" 
      />
    </div>
  ), name: "n8n" },
  { icon: () => (
    <div className="w-12 h-12 flex items-center justify-center">
      <Image
        src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/59c0c88940909cb05dc3e00884fd231a-PYxHDg9d02pPSn1JWEOb4ALpJ8GAkp.png"
        alt="Spline"
        width={40}
        height={40}
      />
    </div>
  ), name: "Spline" },
  { icon: () => (
    <div className="w-12 h-12 flex items-center justify-center">
      <Image
        src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/175a2ee393572a4f29a719fc868f367a-2okBlvH9pgrw61A5NBh7U363MRvlXK.png"
        alt="Figma"
        width={40}
        height={40}
      />
    </div>
  ), name: "Figma" },
  { icon: () => ( // Use local PNG for Cursor logo
    <div className="w-12 h-12 flex items-center justify-center">
      <Image
        src="/cursor.png" // Use local path from public directory
        alt="Cursor"
        width={40} // Increased size
        height={40} // Increased size
        // Removed filter invert class, assuming PNG has correct colors
      />
    </div>
  ), name: "Cursor" }
]

export default function TechStack() {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null)
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const pauseTimeoutRef = useRef<null | NodeJS.Timeout>(null);
  const [isAutoScrolling, setIsAutoScrolling] = useState(true);

  // Auto-scroll on mobile using requestAnimationFrame for smoothness
  useEffect(() => {
    const isMobile = typeof window !== "undefined" && window.innerWidth < 768;
    const container = scrollContainerRef.current;
    let frame: number | null = null;

    function step() {
      if (!container || !isAutoScrolling) return;
      // If at end, reset to start
      if (container.scrollLeft + container.offsetWidth >= container.scrollWidth - 2) {
        container.scrollTo({ left: 0, behavior: "auto" });
      } else {
        container.scrollTo({ left: container.scrollLeft + 0.5, behavior: "auto" }); // slower, smoother
      }
      frame = requestAnimationFrame(step);
    }

    if (isMobile && container && isAutoScrolling) {
      frame = requestAnimationFrame(step);
    }

    return () => {
      if (frame) cancelAnimationFrame(frame);
    };
  }, [isAutoScrolling]);

  // Pause auto-scroll on user interaction, resume after 2s
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;
    const isMobile = typeof window !== "undefined" && window.innerWidth < 768;
    if (!isMobile) return;

    const pause = () => {
      setIsAutoScrolling(false);
      if (pauseTimeoutRef.current) clearTimeout(pauseTimeoutRef.current);
      pauseTimeoutRef.current = setTimeout(() => setIsAutoScrolling(true), 2000);
    };

    container.addEventListener("touchstart", pause, { passive: true });
    container.addEventListener("scroll", pause, { passive: true });

    return () => {
      container.removeEventListener("touchstart", pause);
      container.removeEventListener("scroll", pause);
    };
  }, []);

  const handleTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
    setTouchStart(e.targetTouches[0].clientX);
  }

  const handleTouchMove = (e: React.TouchEvent<HTMLDivElement>) => {
    setTouchEnd(e.targetTouches[0].clientX);
  }

  const handleTouchEnd = () => {
    if (scrollContainerRef.current) {
      if (touchStart - touchEnd > 75) {
        // Swipe left
        if (scrollContainerRef.current) {
          scrollContainerRef.current.scrollLeft += 100;
        }
      }
      if (touchStart - touchEnd < -75) {
        // Swipe right
        if (scrollContainerRef.current) {
          scrollContainerRef.current.scrollLeft -= 100;
        }
      }
    }
  }

  return (
    <div
      className="bg-gradient-to-r from-gray-800/80 via-gray-900/80 to-gray-800/80 backdrop-blur-md rounded-lg p-4 overflow-x-auto touch-pan-x scrollbar-hide"
      ref={scrollContainerRef}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      <div className="flex items-center space-x-4 justify-start min-w-max">
        <AnimatePresence>
          {icons.map((item, index) => (
            <motion.div
              key={item.name}
              className={cn(
                "relative p-3 cursor-pointer rounded-lg transition-shadow duration-300",
                hoveredIndex === index && "shadow-lg shadow-[#FF6B6B]/30"
              )}
              onHoverStart={() => setHoveredIndex(index)}
              onHoverEnd={() => setHoveredIndex(null)}
              initial={{ y: 0 }}
              animate={{
                scale: hoveredIndex === index ? 1.2 : 1,
                y: hoveredIndex === index ? "-5%" : 0,
              }}
              transition={{
                type: "spring",
                stiffness: 700,
                damping: 30,
                mass: 1,
                velocity: 5,
              }}
            >
              <motion.div
                animate={{ rotate: hoveredIndex === index ? 360 : 0 }}
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 10,
                  mass: 0.5,
                  velocity: 2,
                }}
              >
                <item.icon />
              </motion.div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </div>
  )
}
