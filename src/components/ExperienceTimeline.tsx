'use client'

import { motion } from 'framer-motion'
import { cn } from "@/lib/utils"

const experiences = [
  {
    period: "Sep 2024 to Today",
    role: "Sales Representative",
    company: "IBC",
  },
  {
    period: "Apr 2024 to Sep 2024",
    role: "Marketing Manager",
    company: "Equilibre Labs",
  },
  {
    period: "Jan 2024 to Jun 2024",
    role: "Community Manager",
    company: "Dirac Finance",
  },
  {
    period: "Nov 2023 to Oct 2024",
    role: "Community Manager & Marketing",
    company: "Matrix Farm",
  },
  {
    period: "Jun 2023 to Oct 2023",
    role: "Moderator",
    company: "PearlFI",
  }
]

export default function ExperienceTimeline() {
  return (
    <motion.div 
      className="relative"
    >
      <motion.div 
        className="absolute left-0 top-0 bottom-0 w-px bg-[#FF6B6B]"
        initial={{ scaleY: 0 }}
        animate={{ scaleY: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      />
      <motion.div 
        className="space-y-8"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
        variants={{
          hidden: {},
          visible: {
            transition: {
              staggerChildren: 0.2
            }
          }
        }}
      >
        {experiences.map((item, index) => (
          <motion.div 
            key={item.period} 
            className="relative pl-8"
            variants={{
              hidden: { opacity: 0, x: -50 },
              visible: { opacity: 1, x: 0 }
            }}
          >
            <motion.div 
              className="absolute left-0 w-2 h-2 -translate-x-1/2 rounded-full bg-[#FF6B6B]"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2 + index * 0.2, duration: 0.3 }}
            />
            <div className="text-[#FF6B6B] text-xl">{item.period}</div>
            <div className="flex items-center gap-2">
              <h3 className={cn(
                "text-6xl font-bold mt-1 bg-gradient-to-r from-[#FF6B6B] to-[#FF8E53] bg-clip-text text-transparent",
                "animate-gradient"
              )}>{item.company}</h3>
            </div>
            <div className="text-xl text-white">{item.role}</div>
          </motion.div>
        ))}
      </motion.div>
    </motion.div>
  )
}
