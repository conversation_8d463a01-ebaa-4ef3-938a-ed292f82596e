import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"

interface Project {
  id: number
  name: string
  description: string
  image: string
  link: string
}

interface HobbyProjectsProps {
  projects: Project[]
}

export default function HobbyProjects({ projects }: HobbyProjectsProps) {
  return (
    <div className="overflow-x-auto pb-4 scrollbar-hide">
      <div className="flex space-x-4" style={{ width: 'max-content' }}>
        {projects.map((project) => (
          <div
            key={project.id}
            className="cursor-pointer"
            onClick={() => window.open(project.link, '_blank', 'noopener,noreferrer')}
          >
            <Card 
              className="bg-[#1A1F2A] border-none w-72 hover:bg-[#2A2F3A] transition-colors duration-200"
            >
              <CardContent className="p-0">
                <div className="h-48 overflow-hidden relative">
                  <Image 
                    src={project.image} 
                    alt={project.name} 
                    fill
                    className={project.name === "AI From Zero to Hero" 
                      ? "object-contain rounded-t-lg bg-gradient-to-br from-[#2A5A3A] to-[#1A3A2A] p-2 transform scale-[1.2]" 
                      : "object-cover object-top rounded-t-lg"
                    }
                  />
                </div>
                <div className="p-4">
                  <h3 className="text-lg font-semibold text-white mb-2">{project.name}</h3>
                  <p className="text-sm text-gray-400">{project.description}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        ))}
      </div>
    </div>
  )
}
