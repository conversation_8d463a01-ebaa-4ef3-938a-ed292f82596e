@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html, body {
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch; /* For iOS momentum scrolling */
    overscroll-behavior-y: contain;
    width: 100%;
    /* Hide scrollbar for all browsers */
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  html::-webkit-scrollbar,
  body::-webkit-scrollbar {
    display: none;
  }

  section {
    scroll-margin-top: 120px;
  }
}

@layer utilities {
  .animate-gradient {
    background-size: 200% auto;
    animation: gradient 8s linear infinite;
  }
  .animate-wave {
    animation: wave 1.5s infinite;
    display: inline-block;
    transform-origin: 70% 70%;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes wave {
  0% { transform: rotate(0deg); }
  20% { transform: rotate(-14deg); }
  40% { transform: rotate(14deg); }
  60% { transform: rotate(-4deg); }
  80% { transform: rotate(4deg); }
  100% { transform: rotate(0deg); }
}

@keyframes shine {
  0% {
    transform: translateX(-100%);
  }
  50%, 100% {
    transform: translateX(100%);
  }
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 5px 0px rgba(255, 107, 107, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 15px 5px rgba(255, 107, 107, 0.7);
    transform: scale(1.03);
  }
  100% {
    box-shadow: 0 0 5px 0px rgba(255, 107, 107, 0.5);
    transform: scale(1);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
}

body {
  @apply bg-black text-white;
  font-family: Arial, Helvetica, sans-serif;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  min-height: 100%;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@media (max-width: 768px) {
  .waves-background {
    opacity: 0.3;
    pointer-events: none;
    animation: none;
    transform: none;
  }
}
