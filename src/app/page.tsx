'use client'

import { useState } from "react"
import { HelpCircle, Home, User, Code, History, MessageSquare } from 'lucide-react'
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"
import HobbyProjects from "@/components/HobbyProjects"
import TypedName from '@/components/TypedName'
import { Timeline } from "@/components/ui/timeline"
import { SparklesCore } from "@/components/ui/sparkles"
import { Waves } from "@/components/ui/waves-background"
import { AnimeNavBar } from "@/components/ui/anime-navbar"
import { FaTwitter, FaTelegram, FaYoutube, FaGithub } from 'react-icons/fa';
import { useEffect } from "react";
import { Globe } from 'lucide-react'
import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import CalendarSchedule from "@/components/CalendarSchedule"

const hobbyProjects = [
  {
    id: 1,
    name: "My Favourite AI Tools",
    description: "A curated collection of the most powerful AI tools and models.",
    image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-Gq9jjRbmFRwwWBaBmwfYgvVjVjzYbD.png",
    link: "https://aitools.oddofrancesco.com"
  },
  {
    id: 2,
    name: "Mermaid PNG Maker",
    description: "Create beautiful diagrams with Mermaid.js.",
    image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-Bxne5UCYnWdkoDMMfqmMgfbtkBigHF.png",
    link: "https://mermaidgraphs.vercel.app/"
  },
  {
    id: 3,
    name: "Compound Interest Graph",
    description: "Visualize your investment growth over time with adjustable parameters.",
    image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Capture-2024-11-21-203102-4mYhdygFwWiH5BteTIKtLwroVNLX8z.png",
    link: "https://cicalculator.vercel.app/"
  },
  {
    id: 4,
    name: "Custom Invoice Maker",
    description: "Create professional and customizable invoices with ease.",
    image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/822fd4738cd6761a970d4ffd32b9c0d2-B0LsxYL4OAOWOFJxWWtcqr1MD5jlYq.png",
    link: "https://invoicemaker.vercel.app/"
  },
  {
    id: 5,
    name: "Proof of Learning",
    description: "A fun way of learning web3 words, block by block.",
    image: "/pol.jpg",
    link: "https://proofoflearning.vercel.app/"
  },
  {
    id: 6,
    name: "AI From Zero to Hero",
    description: "Teaching easy AI concepts to non-technical people.",
    image: "/ai-from-zero-to-hero.png",
    link: "https://ai.oddofrancesco.com/"
  }
]

function TimelineDemo() {
  const data = [
    {
      title: "2024 - Present",
      content: (
        <div>
          <p className="text-neutral-200 text-lg md:text-2xl font-normal mb-2">Head of AI</p>
          <p className={cn(
            "text-xl md:text-3xl font-bold mb-4 bg-gradient-to-r from-[#FF6B6B] to-[#FF8E53] bg-clip-text text-transparent",
            "animate-gradient"
          )}>IBC</p>
          <p className="text-neutral-200 text-base md:text-xl font-normal mb-4">
          I help businesses identify and implement artificial intelligence solutions to improve efficiency, decision-making, and innovation.
          </p>
          <div className="flex gap-2 mt-2">
            <a
              href="https://x.com/MarioNawfal"
              target="_blank"
              rel="noopener noreferrer"
              className="p-2 rounded-lg bg-[#1A1F2A]/50 hover:bg-[#1A1F2A] transition-colors duration-200"
            >
              <FaTwitter className="w-5 h-5 text-gray-400 hover:text-[#1DA1F2]" />
            </a>
            <a
              href="https://ibcgroup.io"
              target="_blank"
              rel="noopener noreferrer"
              className="p-2 rounded-lg bg-[#1A1F2A]/50 hover:bg-[#1A1F2A] transition-colors duration-200"
            >
              <Globe className="w-5 h-5 text-gray-400" />
            </a>
          </div>
        </div>
      ),
    },
    {
      title: "Apr 2024 to Sep 2024",
      content: (
        <div>
          <p className="text-neutral-200 text-lg md:text-2xl font-normal mb-2">Marketing Manager</p>
          <p className={cn(
            "text-xl md:text-3xl font-bold mb-4 bg-gradient-to-r from-[#FF6B6B] to-[#FF8E53] bg-clip-text text-transparent",
            "animate-gradient"
          )}>Equilibre Labs</p>
          <p className="text-neutral-200 text-base md:text-xl font-normal mb-4">
            Led marketing initiatives for blockchain and DeFi projects.
          </p>
        </div>
      ),
    },
    {
      title: "Jan 2024 to Jun 2024",
      content: (
        <div>
          <p className="text-neutral-200 text-lg md:text-2xl font-normal mb-2">Community Manager</p>
          <p className={cn(
            "text-xl md:text-3xl font-bold mb-4 bg-gradient-to-r from-[#FF6B6B] to-[#FF8E53] bg-clip-text text-transparent",
            "animate-gradient"
          )}>Dirac Finance</p>
          <p className="text-neutral-200 text-base md:text-xl font-normal mb-4">
            Managed and grew the community for a decentralized finance platform.
          </p>
        </div>
      ),
    },
    {
      title: "Nov 2023 to Oct 2024",
      content: (
        <div>
          <p className="text-neutral-200 text-lg md:text-2xl font-normal mb-2">Community Manager & Marketing</p>
          <p className={cn(
            "text-xl md:text-3xl font-bold mb-4 bg-gradient-to-r from-[#FF6B6B] to-[#FF8E53] bg-clip-text text-transparent",
            "animate-gradient"
          )}>Matrix Farm</p>
          <p className="text-neutral-200 text-base md:text-xl font-normal mb-4">
            Handled both community management and marketing strategies for a Web3 project.
          </p>
        </div>
      ),
    },
    {
      title: "Jun 2023 to Oct 2023",
      content: (
        <div>
          <p className="text-neutral-200 text-lg md:text-2xl font-normal mb-2">Moderator</p>
          <p className={cn(
            "text-xl md:text-3xl font-bold mb-4 bg-gradient-to-r from-[#FF6B6B] to-[#FF8E53] bg-clip-text text-transparent",
            "animate-gradient"
          )}>PearlFI</p>
          <p className="text-neutral-200 text-base md:text-xl font-normal mb-4">
            Moderated community discussions and provided support for a DeFi platform.
          </p>
        </div>
      ),
    }
  ];

  return (
    <div className="w-full">
      <Timeline data={data} />
    </div>
  );
}

const navItems = [
  {
    name: "Home",
    url: "#home",
    icon: Home
  },
  {
    name: "Projects",
    url: "#projects",
    icon: Code
  },
  {
    name: "Experience",
    url: "#experience",
    icon: History
  },
  {
    name: "Hobbies",
    url: "#hobbies",
    icon: User
  }
];

export default function Portfolio() {
  const [telegramGlowing, setTelegramGlowing] = useState(false);

  // Effect for initial navigation
  useEffect(() => {
    if (!window.location.hash) {
      window.location.hash = 'home';
    }

    const section = window.location.hash ? window.location.hash : '#home';
    const element = document.querySelector(section);
    element?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Effect for Telegram icon glow on page load
  useEffect(() => {
    setTelegramGlowing(true);

    const timer = setTimeout(() => {
      setTelegramGlowing(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="min-h-screen bg-black touch-pan-y">
      {/* Navigation Bar */}
      <AnimeNavBar
        items={navItems}
        className=""
        defaultActive="Home"
      />

      {/* Chat Button - Positioned absolutely to the right of the screen with padding to avoid scrollbar */}
      <div className="fixed top-8 right-8 z-[9999] md:block hidden">
        <a
          href="#calendar"
          className="group block"
        >
          <div
            className="p-[2px] rounded-lg bg-gradient-to-r from-[#FF6B6B] to-[#FF8E53] transition-all duration-300 group-hover:animate-pulse-glow"
            style={{
              transformStyle: "preserve-3d",
            }}
          >
            <div
              className="py-2 px-4 rounded-[6px] bg-[#1A1F2A] flex items-center gap-2 group-hover:animate-float"
            >
              <MessageSquare className="w-4 h-4 text-[#FF6B6B] group-hover:animate-spin-slow" />
              <span className="text-white font-medium">Let&apos;s Chat!</span>
            </div>
          </div>
        </a>
      </div>

      {/* Mobile Chat Button - Shown only on mobile */}
      <div className="fixed bottom-6 right-6 z-[9999] md:hidden block">
        <a
          href="#calendar"
          className="group block"
        >
          <div
            className="p-[2px] rounded-full bg-gradient-to-r from-[#FF6B6B] to-[#FF8E53] transition-all duration-300 group-hover:animate-pulse-glow shadow-lg shadow-[#FF6B6B]/20"
          >
            <div
              className="p-3 rounded-full bg-[#1A1F2A] flex items-center justify-center"
            >
              <MessageSquare className="w-6 h-6 text-[#FF6B6B]" />
            </div>
          </div>
        </a>
      </div>

      {/* Background effects */}
      <div className="fixed inset-0 z-0 pointer-events-none touch-none">
        <Waves
          className="pointer-events-none waves-background touch-none"
          lineColor="rgba(255,107,107,0.2)"
          waveSpeedX={0.015}
          waveSpeedY={0.01}
          waveAmpX={20}
          waveAmpY={10}
        />

        <div className="absolute inset-0">
          <SparklesCore
            id="tsparticlesfullscreen"
            background="transparent"
            minSize={0.6}
            maxSize={1.4}
            particleColor="#FF6B6B"
            particleDensity={70}
            speed={1}
            className="w-full h-full"
          />
        </div>
      </div>

      {/* Main content */}
      {/* Removed px-4 from main container */}
      {/* Added px-4 back to main container */}
      <main className="relative z-20 container mx-auto px-4 pt-32 pb-24">
        {/* Hero Section - Removed px-4 */}
        <section id="home" className="min-h-[calc(100vh-8rem)] flex flex-col justify-center mb-32">
          <div className="flex items-center gap-2 text-3xl mb-4">
            <span className={cn(
              "bg-gradient-to-r from-[#FF6B6B] to-[#FF8E53] bg-clip-text text-transparent",
              "animate-gradient"
            )}>
              Ciao!
            </span>
            <span className="animate-wave text-4xl">👋🏻</span>
            <span className={cn(
              "bg-gradient-to-r from-[#FF6B6B] to-[#FF8E53] bg-clip-text text-transparent",
              "animate-gradient"
            )}>
              I&apos;m
            </span>
          </div>

          <h1 className={cn(
            "text-5xl md:text-7xl lg:text-8xl font-bold mb-6 bg-gradient-to-r from-[#FF6B6B] to-[#FF8E53] bg-clip-text text-transparent",
            "animate-gradient"
          )}>
            <TypedName />
          </h1>

          <h2 className="text-4xl text-gray-400 mb-4">
            Italian Web3 Founder & AI Consultant
          </h2>

          <div className="flex gap-6 items-center mt-2">
            <a
              href="https://twitter.com/OxFrancesco_"
              target="_blank"
              rel="noopener noreferrer"
              className="group relative"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-[#1DA1F2]/20 to-[#1DA1F2]/10 rounded-xl blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <div className="relative p-4 rounded-xl bg-[#1A1F2A]/50 border border-[#1DA1F2]/10 backdrop-blur-sm transition-all duration-300 group-hover:border-[#1DA1F2]/30">
                <FaTwitter className="w-7 h-7 text-gray-400 group-hover:text-[#1DA1F2] transition-colors duration-300" />
              </div>
            </a>
            <a
              href="https://t.me/Francesco_oddo"
              target="_blank"
              rel="noopener noreferrer"
              className="group relative"
              id="telegram-icon"
            >
              <div className={`absolute inset-0 bg-gradient-to-r from-[#0088cc]/40 to-[#0088cc]/20 rounded-xl blur-md ${telegramGlowing ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'} transition-opacity duration-300`} />
              <div className={`relative p-4 rounded-xl bg-[#1A1F2A]/50 border ${telegramGlowing ? 'border-[#0088cc]/70 shadow-lg shadow-[#0088cc]/30' : 'border-[#0088cc]/10 group-hover:border-[#0088cc]/30'} backdrop-blur-sm transition-all duration-300`}>
                <FaTelegram className={`w-7 h-7 ${telegramGlowing ? 'text-[#0088cc] animate-telegram-glow' : 'text-gray-400 group-hover:text-[#0088cc]'} transition-colors duration-300`} />
              </div>
            </a>
            <a
              href="https://www.youtube.com/@OxFrancesco"
              target="_blank"
              rel="noopener noreferrer"
              className="group relative"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-[#FF0000]/20 to-[#FF0000]/10 rounded-xl blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <div className="relative p-4 rounded-xl bg-[#1A1F2A]/50 border border-[#FF0000]/10 backdrop-blur-sm transition-all duration-300 group-hover:border-[#FF0000]/30">
                <FaYoutube className="w-7 h-7 text-gray-400 group-hover:text-[#FF0000] transition-colors duration-300" />
              </div>
            </a>
            <a
              href="https://github.com/OxFrancesco"
              target="_blank"
              rel="noopener noreferrer"
              className="group relative"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-[#6e5494]/20 to-[#6e5494]/10 rounded-xl blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <div className="relative p-4 rounded-xl bg-[#1A1F2A]/50 border border-[#6e5494]/10 backdrop-blur-sm transition-all duration-300 group-hover:border-[#6e5494]/30">
                <FaGithub className="w-7 h-7 text-gray-400 group-hover:text-[#6e5494] transition-colors duration-300" />
              </div>
            </a>
          </div>
        </section>

        {/* Projects Section */}
        {/* Projects Section - No px-4 needed */}
        <section id="projects" className="mb-32">
          <h2 className={cn(
            "text-4xl font-bold mb-8", // Reverted title alignment
            "bg-gradient-to-r from-[#FF6B6B] to-[#FF8E53] bg-clip-text text-transparent animate-gradient"
          )}>Founded Projects</h2>
          {/* Re-added overflow-x-auto and scrollbar-hide */}
          {/* Removed overflow-x-auto and scrollbar-hide */}
          {/* Outer container handles desktop scrolling */}
          <div className="w-full md:overflow-x-auto md:pb-4 md:scrollbar-hide">
            {/* Inner container: flex-col on mobile, flex-row on desktop */}
            <div className="flex flex-col gap-4 md:flex-row md:gap-8">
              {/* BeraGod Project */}
              <div className="cursor-pointer w-full md:flex-none md:w-[340px] lg:w-[500px]" onClick={() => window.open('https://beragod.app', '_blank', 'noopener,noreferrer')}>
                <Card className="bg-[#1A1F2A] border-none hover:bg-[#2A2F3A] transition-colors duration-200">
                  <CardContent className="p-0">
                    <div className="h-48 md:h-64 overflow-hidden relative">
                      <Image
                        src="/beragod.png"
                        alt="BeraGod Project"
                        fill
                        className="object-cover object-top rounded-t-lg"
                      />
                    </div>
                    <div className="p-4">
                      <h3 className="text-lg font-semibold text-white mb-2">BeraGod</h3>
                      <div className="flex gap-2">
                        <a
                          href="https://t.me/BeraGodBot"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-2 rounded-lg bg-[#1A1F2A] hover:bg-[#2A2F3A] transition-colors duration-200"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <FaTelegram className="w-5 h-5 text-gray-400 hover:text-[#0088cc]" />
                        </a>
                        <a
                          href="https://beragod.app"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-2 rounded-lg bg-[#1A1F2A] hover:bg-[#2A2F3A] transition-colors duration-200"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Globe className="w-5 h-5 text-gray-400" />
                        </a>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* BuddyIntels Project */}
              <div className="cursor-pointer w-full md:flex-none md:w-[340px] lg:w-[500px]" onClick={() => window.open('https://buddyintels.oddofrancesco.com/', '_blank', 'noopener,noreferrer')}>
                <Card className="bg-[#1A1F2A] border-none hover:bg-[#2A2F3A] transition-colors duration-200">
                  <CardContent className="p-0">
                    <div className="h-48 md:h-64 overflow-hidden relative">
                      <Image
                        src="/buddyintels.png"
                        alt="BuddyIntels Project"
                        fill
                        className="object-cover object-center rounded-t-lg"
                      />
                    </div>
                    <div className="p-4">
                      <h3 className="text-lg font-semibold text-white mb-2">BuddyIntels</h3>
                      <div className="flex gap-2">
                        <a
                          href="https://t.me/BuddyIntelsBot"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-2 rounded-lg bg-[#1A1F2A] hover:bg-[#2A2F3A] transition-colors duration-200"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <FaTelegram className="w-5 h-5 text-gray-400 hover:text-[#0088cc]" />
                        </a>
                        <a
                          href="https://buddyintels.oddofrancesco.com/"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-2 rounded-lg bg-[#1A1F2A] hover:bg-[#2A2F3A] transition-colors duration-200"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Globe className="w-5 h-5 text-gray-400" />
                        </a>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>
        {/* Experience Section */}
        {/* Experience Section - Removed px-4 */}
        <section id="experience" className="mb-32">
          <h2 className={cn(
            "text-4xl font-bold mb-8",
            "bg-gradient-to-r from-[#FF6B6B] to-[#FF8E53] bg-clip-text text-transparent animate-gradient"
          )}>Experience</h2>
          <TimelineDemo />
        </section>

        {/* Hobbies Section */}
        {/* Hobbies Section - Removed px-4 */}
        <section id="hobbies" className="mb-32">
          <div className="flex justify-between items-center mb-8">
            <h2 className={cn(
              "text-4xl font-bold bg-gradient-to-r from-[#FF6B6B] to-[#FF8E53] bg-clip-text text-transparent animate-gradient"
            )}>My Hobbies</h2>
            <TooltipProvider>
              <Tooltip delayDuration={0}>
                <TooltipTrigger asChild>
                  <div className="bg-gray-800/50 p-2 rounded-lg cursor-help">
                    <HelpCircle className="h-5 w-5 text-[#FF6B6B]" />
                  </div>
                </TooltipTrigger>
                <TooltipContent sideOffset={5} className="max-w-xs">
                  <p>I&apos;m the materialization of the Dunning-Kruger Effect</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <p className="text-xl text-gray-400 mb-8">
            My favorite thing in the world is learning to do new stuff and creating App UIs & Logic that will never ship...
          </p>
          <HobbyProjects projects={hobbyProjects} />
        </section>




        {/* Calendar Section */}
        <section id="calendar" className="mb-32">
          <h2 className={cn(
            "text-4xl font-bold mb-8",
            "bg-gradient-to-r from-[#FF6B6B] to-[#FF8E53] bg-clip-text text-transparent animate-gradient"
          )}>Book a Call</h2>
          <p className="text-xl text-gray-400 mb-8">
            Want to discuss a project or just have a chat? Feel free to schedule a call with me.
          </p>
          <div className="bg-[#1A1F2A]/50 backdrop-blur-md rounded-xl p-0 border border-[#FF6B6B]/10 hover:border-[#FF6B6B]/30 transition-colors duration-300">
            <CalendarSchedule />
          </div>
        </section>
      </main>

      <footer className="relative z-20 text-center py-6 text-gray-400 bg-[#1A1F2A]">
        Made with 🧡 by myself for myself!
      </footer>
    </div>
  );
}