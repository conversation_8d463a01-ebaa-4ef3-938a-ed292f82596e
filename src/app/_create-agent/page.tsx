'use client'

import { Waves } from "@/components/ui/waves-background"
import { SparklesCore } from "@/components/ui/sparkles"
import { cn } from "@/lib/utils"
import Link from "next/link"
import { Home } from "lucide-react"

export default function CreateAgentPage() {
  return (
    <div className="min-h-screen bg-black touch-pan-y">
      {/* Background effects */}
      <div className="fixed inset-0 z-0 pointer-events-none touch-none">
        <Waves
          className="pointer-events-none waves-background touch-none"
          lineColor="rgba(255,107,107,0.2)" // Same as main page
          waveSpeedX={0.015}
          waveSpeedY={0.01}
          waveAmpX={20}
          waveAmpY={10}
        />
        <div className="absolute inset-0">
          <SparklesCore
            id="tsparticlesfullscreen-create-agent" // Unique ID for this instance
            background="transparent"
            minSize={0.6}
            maxSize={1.4}
            particleColor="#FF6B6B" // Same as main page
            particleDensity={70}
            speed={1}
            className="w-full h-full"
          />
        </div>
      </div>

      {/* Navbar Placeholder - A real navbar will be added in a later step via layout */}
      {/* For now, a simple link back to home might be useful for testing */}
      <div className="fixed top-0 left-0 right-0 z-[9999] flex justify-center pt-8">
        <Link href="/" className="px-4 py-2 bg-slate-800 text-white rounded-lg hover:bg-slate-700 transition-colors flex items-center gap-2">
          <Home size={16} />
          Back to Home
        </Link>
      </div>

      {/* Main content */}
      <main className="relative z-20 container mx-auto px-4 pt-32 pb-24">
        <section id="create-agent-hero" className="min-h-[calc(100vh-8rem)] flex flex-col justify-center items-center text-center">
          <h1 className={cn(
            "text-5xl md:text-7xl lg:text-8xl font-bold mb-6 bg-gradient-to-r from-[#FF6B6B] to-[#FF8E53] bg-clip-text text-transparent",
            "animate-gradient" // Assuming this class provides the gradient animation
          )}>
            Create an Agent!
          </h1>
          <p className="text-xl md:text-2xl text-gray-400 mb-8 max-w-2xl">
            This is the landing page for our AI Agents Automation company.
            We&apos;ll help you build amazing AI agents tailored to your needs.
            (Placeholder content)
          </p>
          {/* You can add more components here, like a call to action button, features list, etc. */}
          {/* Example of a button, assuming you have a Button component */}
          {/* <Button size="lg" className="bg-gradient-to-r from-[#FF6B6B] to-[#FF8E53] hover:opacity-90 text-white">
            Get Started
          </Button> */}
        </section>
      </main>

      <footer className="relative z-20 text-center py-6 text-gray-400 bg-[#1A1F2A]">
        AI Agents Automation &copy; {new Date().getFullYear()}
      </footer>
    </div>
  )
}
